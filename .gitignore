# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
out/

# Configuration files (contains sensitive API keys)
config.json

# Temporary files
*.tmp
*.temp
*.log
image.tmp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Electron specific
.electron-gyp/
.electron-rebuild/

# Local environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Local Node.js installation
local-node/

# Coverage and test files
coverage/
.nyc_output

# Package files
*.tgz
.yarn-integrity

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Build artifacts
*.exe
*.msi
*.dmg
*.AppImage
*.deb
*.rpm

roon-discord-config.json

test-results/screenshots/

test-results/

# Test secrets (contains real API keys for testing)
test/secrets.json
