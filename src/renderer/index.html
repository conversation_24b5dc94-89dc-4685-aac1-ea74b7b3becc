<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roon Discord Rich Presence</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <div id="app">
        <!-- Navigation -->
        <nav class="nav-bar">
            <div class="nav-brand">
                <img src="../../assets/icon.png" alt="Roon Discord" class="nav-logo">
                <h1>Roon Discord Rich Presence</h1>
            </div>
            <div class="nav-tabs">
                <button class="nav-tab active" data-tab="status">Status</button>
                <button class="nav-tab" data-tab="config">Configuration</button>
                <button class="nav-tab" data-tab="logs">Logs</button>
                <button class="nav-tab" data-tab="help">Help</button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Status Tab -->
            <div id="status-tab" class="tab-content active">
                <!-- Currently Playing Section -->
                <div class="now-playing-section">
                    <h2>Currently Playing</h2>
                    <div class="now-playing-card" id="now-playing">
                        <div class="album-art">
                            <img id="album-image" src="" alt="Album Art" style="display: none;">
                            <div class="no-music" id="no-music">
                                <span>♪</span>
                                <p>No music playing</p>
                            </div>
                        </div>
                        <div class="track-info">
                            <h3 id="track-title" data-testid="track-title">-</h3>
                            <p id="track-artist" data-testid="track-artist">-</p>
                            <p id="track-album" data-testid="track-album">-</p>
                            <p id="track-zone" data-testid="track-zone" class="zone-info">-</p>
                            <div class="track-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-fill"></div>
                                </div>
                                <div class="time-info">
                                    <span id="current-time">0:00</span>
                                    <span id="total-time">0:00</span>
                                </div>
                            </div>
                            <div class="track-actions" id="track-actions" style="display: none;">
                                <button class="btn btn-small btn-spotify" id="spotify-link" data-testid="spotify-link" style="display: none;">
                                    🎵 Open in Spotify
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="status-grid">
                    <!-- Connection Status Cards -->
                    <div class="status-card" data-service="discord">
                        <div class="status-header">
                            <h3>Discord</h3>
                            <div class="status-indicator" id="discord-status">
                                <span class="status-dot disconnected" data-testid="discord-status-dot"></span>
                                <span class="status-text" data-testid="discord-status-text">Disconnected</span>
                            </div>
                        </div>
                        <div class="status-details">
                            <p id="discord-details" data-testid="discord-details">Not connected to Discord</p>
                            <button class="btn btn-small" id="discord-reconnect" data-testid="discord-reconnect">Reconnect</button>
                        </div>
                    </div>

                    <div class="status-card" data-service="roon">
                        <div class="status-header">
                            <h3>Roon</h3>
                            <div class="status-indicator" id="roon-status">
                                <span class="status-dot disconnected" data-testid="roon-status-dot"></span>
                                <span class="status-text" data-testid="roon-status-text">Disconnected</span>
                            </div>
                        </div>
                        <div class="status-details">
                            <p id="roon-details" data-testid="roon-details">Not connected to Roon</p>
                            <button class="btn btn-small" id="roon-reconnect" data-testid="roon-reconnect">Reconnect</button>
                        </div>
                    </div>

                    <div class="status-card" data-service="spotify">
                        <div class="status-header">
                            <h3>Spotify</h3>
                            <div class="status-indicator" id="spotify-status">
                                <span class="status-dot disconnected" data-testid="spotify-status-dot"></span>
                                <span class="status-text" data-testid="spotify-status-text">Disconnected</span>
                            </div>
                        </div>
                        <div class="status-details">
                            <p id="spotify-details" data-testid="spotify-details">Not connected to Spotify API</p>
                            <button class="btn btn-small" id="spotify-reconnect" data-testid="spotify-reconnect">Reconnect</button>
                        </div>
                    </div>

                    <div class="status-card" data-service="imgur">
                        <div class="status-header">
                            <h3>Imgur</h3>
                            <div class="status-indicator" id="imgur-status">
                                <span class="status-dot disconnected" data-testid="imgur-status-dot"></span>
                                <span class="status-text" data-testid="imgur-status-text">Disconnected</span>
                            </div>
                        </div>
                        <div class="status-details">
                            <p id="imgur-details" data-testid="imgur-details">Not connected to Imgur API</p>
                            <button class="btn btn-small" id="imgur-reconnect" data-testid="imgur-reconnect">Reconnect</button>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" id="reconnect-all" data-testid="reconnect-all">Reconnect All</button>
                    <button class="btn btn-secondary" id="clear-activity" data-testid="clear-activity">Clear Discord Activity</button>
                    <button class="btn btn-secondary" id="test-activity" data-testid="test-activity">Test Discord Activity</button>
                    <button class="btn btn-danger" id="quit-app" data-testid="quit-app">Quit Application</button>
                </div>

                <!-- Debug Controls (Development Only) -->
                <div class="debug-controls" id="debug-controls" style="display: none;">
                    <h3>🐛 Debug Controls</h3>
                    <div class="debug-buttons">
                        <button class="btn btn-small" id="toggle-debug-mode">Toggle Debug Mode</button>
                        <button class="btn btn-small" id="generate-diagnostic-report">Generate Report</button>
                        <button class="btn btn-small" id="clear-diagnostics">Clear Diagnostics</button>
                    </div>
                    <div class="debug-status">
                        <span class="debug-indicator" id="debug-mode-indicator">Debug: OFF</span>
                        <span class="debug-info" id="debug-info"></span>
                    </div>
                </div>
            </div>

            <!-- Configuration Tab -->
            <div id="config-tab" class="tab-content">
                <div class="config-section">
                    <h2>API Configuration</h2>
                    <p class="config-description">Configure your API keys and settings below. All settings are saved automatically.</p>
                    
                    <!-- Discord Configuration -->
                    <div class="config-group">
                        <h3>Discord</h3>
                        <div class="form-group">
                            <label for="discord-client-id">Client ID</label>
                            <input type="text" id="discord-client-id" placeholder="Enter Discord Application Client ID">
                            <button class="btn btn-small test-btn" id="test-discord">Save and Test Connection</button>
                        </div>
                        <p class="help-text">
                            Get your Discord Client ID from the 
                            <a href="#" onclick="require('electron').shell.openExternal('https://discord.com/developers/applications')">Discord Developer Portal</a>
                        </p>
                    </div>

                    <!-- Spotify Configuration -->
                    <div class="config-group">
                        <h3>Spotify</h3>
                        <div class="form-group">
                            <label for="spotify-client-id">Client ID</label>
                            <input type="text" id="spotify-client-id" placeholder="Enter Spotify Client ID">
                        </div>
                        <div class="form-group">
                            <label for="spotify-client-secret">Client Secret</label>
                            <input type="password" id="spotify-client-secret" placeholder="Enter Spotify Client Secret">
                            <button class="btn btn-small test-btn" id="test-spotify">Save and Test Connection</button>
                        </div>
                        <p class="help-text">
                            Get your Spotify credentials from the 
                            <a href="#" onclick="require('electron').shell.openExternal('https://developer.spotify.com/dashboard')">Spotify Developer Dashboard</a>
                        </p>
                    </div>

                    <!-- Imgur Configuration -->
                    <div class="config-group">
                        <h3>Imgur</h3>
                        <div class="form-group">
                            <label for="imgur-client-id">Client ID</label>
                            <input type="text" id="imgur-client-id" placeholder="Enter Imgur Client ID">
                        </div>
                        <div class="form-group">
                            <label for="imgur-client-secret">Client Secret (Optional)</label>
                            <input type="password" id="imgur-client-secret" placeholder="Enter Imgur Client Secret for authenticated uploads">
                            <button class="btn btn-small test-btn" id="test-imgur">Save and Test Connection</button>
                        </div>
                        <p class="help-text">
                            <strong>Anonymous uploads:</strong> Only Client ID required (lower rate limits)<br>
                            <strong>Authenticated uploads:</strong> Both Client ID and Secret required (higher rate limits)<br>
                            Get your credentials from
                            <a href="#" onclick="require('electron').shell.openExternal('https://api.imgur.com/oauth2/addclient')">Imgur API Registration</a>
                        </p>
                    </div>

                    <!-- Roon Configuration -->
                    <div class="config-group">
                        <h3>Roon</h3>
                        <div class="form-group">
                            <label for="roon-core-ip">Core IP (Optional)</label>
                            <input type="text" id="roon-core-ip" placeholder="Leave empty for auto-discovery">
                        </div>
                        <div class="form-group">
                            <label for="roon-zone-id">Zone ID (Optional)</label>
                            <input type="text" id="roon-zone-id" placeholder="Leave empty for auto-selection">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="roon-use-discovery" checked>
                                Use auto-discovery
                            </label>
                        </div>
                    </div>

                    <!-- App Settings -->
                    <div class="config-group">
                        <h3>Application Settings</h3>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="app-minimize-to-tray" checked>
                                Minimize to system tray
                            </label>
                        </div>

                    </div>

                    <!-- Configuration Actions -->
                    <div class="config-actions">
                        <button class="btn btn-primary" id="save-config">Save Configuration</button>
                        <button class="btn btn-secondary" id="reset-config">Reset to Defaults</button>
                        <button class="btn btn-secondary" id="export-config">Export Config</button>
                        <button class="btn btn-secondary" id="import-config">Import Config</button>
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div id="logs-tab" class="tab-content">
                <div class="logs-section">
                    <div class="logs-header">
                        <h2>Application Logs</h2>
                        <div class="logs-controls">
                            <button class="btn btn-small" id="clear-logs">Clear Logs</button>
                            <button class="btn btn-small" id="export-logs">Export Logs</button>
                        </div>
                    </div>
                    <div class="logs-container">
                        <div id="logs-content" class="logs-content">
                            <p class="log-entry info">Application started</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Tab -->
            <div id="help-tab" class="tab-content">
                <div class="help-section">
                    <h2 style="color: var(--text-primary); text-align: center; margin-bottom: 2rem;">
                        🎵 Help & Documentation
                    </h2>

                    <div style="text-align: center; margin-bottom: 2rem;">
                        <p style="color: var(--text-secondary); font-size: 1.1rem; margin-bottom: 1.5rem;">
                            Need help setting up API keys or configuring services? Our comprehensive guide has you covered!
                        </p>

                        <button class="btn btn-primary" onclick="openHelpWindow()" style="font-size: 1.1rem; padding: 1rem 2rem;">
                            📖 Open Full Help Guide
                        </button>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-top: 2rem;">
                        <div class="help-quick-card">
                            <h3>🎮 Discord Setup</h3>
                            <p>Create a Discord application and get your Client ID for rich presence integration.</p>
                            <button class="btn btn-secondary btn-small" onclick="openHelpSection('discord')">Learn More</button>
                        </div>

                        <div class="help-quick-card">
                            <h3>🎵 Roon Configuration</h3>
                            <p>Connect to your Roon Core using automatic discovery or manual IP configuration.</p>
                            <button class="btn btn-secondary btn-small" onclick="openHelpSection('roon')">Learn More</button>
                        </div>

                        <div class="help-quick-card">
                            <h3>🎧 Spotify Integration</h3>
                            <p>Optional: Add Spotify links to your Discord status for easy music sharing.</p>
                            <button class="btn btn-secondary btn-small" onclick="openHelpSection('spotify')">Learn More</button>
                        </div>

                        <div class="help-quick-card">
                            <h3>🖼️ Imgur Setup</h3>
                            <p>Optional: Upload album artwork to enhance your Discord rich presence.</p>
                            <button class="btn btn-secondary btn-small" onclick="openHelpSection('imgur')">Learn More</button>
                        </div>
                    </div>

                    <div style="margin-top: 2rem; padding: 1.5rem; background: var(--bg-secondary); border-radius: 8px; border: 1px solid var(--border-primary);">
                        <h3 style="color: var(--text-primary); margin-bottom: 1rem;">🔧 Quick Troubleshooting</h3>
                        <ul style="color: var(--text-secondary); line-height: 1.6;">
                            <li><strong>Discord not showing status:</strong> Check your Client ID and Discord privacy settings</li>
                            <li><strong>Can't connect to Roon:</strong> Ensure Roon Core is running and accept the pairing request</li>
                            <li><strong>No album artwork:</strong> Verify your Imgur Client ID or check internet connection</li>
                            <li><strong>Spotify links not working:</strong> Double-check both Client ID and Client Secret</li>
                        </ul>
                        <button class="btn btn-secondary btn-small" onclick="openHelpSection('troubleshooting')" style="margin-top: 1rem;">
                            View Full Troubleshooting Guide
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/tabs.js"></script>
    <script src="js/status.js"></script>
    <script src="js/config.js"></script>
    <script src="js/logs.js"></script>
    <script src="js/debug.js"></script>
    <script src="js/help.js"></script>
</body>
</html>
