/* Dark Theme Color Variables */
:root {
    /* Dark Theme Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3d3d3d;
    --bg-card: rgba(45, 45, 45, 0.9);
    --bg-nav: rgba(26, 26, 26, 0.95);

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;

    /* Accent Colors */
    --accent-primary: #ff4444;
    --accent-secondary: #cc3333;
    --accent-hover: #ff6666;

    /* Border Colors */
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.05);

    /* Status Colors */
    --status-success: #4ade80;
    --status-warning: #fbbf24;
    --status-error: #f87171;
    --status-info: #60a5fa;

    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
    --shadow-heavy: rgba(0, 0, 0, 0.7);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    overflow: hidden;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navigation */
.nav-bar {
    background: var(--bg-nav);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-primary);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px var(--shadow-medium);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.nav-brand h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.nav-tabs {
    display: flex;
    gap: 0.5rem;
}

.nav-tab {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-tab:hover {
    background: var(--border-secondary);
    color: var(--text-primary);
}

.nav-tab.active {
    background: var(--accent-primary);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 68, 68, 0.3);
}

/* Main Content */
.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 68, 68, 0.3);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Status Grid */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.status-card {
    background: var(--bg-card);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px var(--shadow-light);
    border: 1px solid var(--border-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: var(--status-success);
}

.status-dot.connecting {
    background: var(--status-warning);
}

.status-dot.disconnected {
    background: var(--status-error);
}

.status-dot.error {
    background: var(--accent-secondary);
}

.status-dot.warning {
    background: var(--status-warning);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Configuration Status Styles */
.config-status {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-summary h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
}

.status-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.status-item {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid transparent;
}

.status-item.missing {
    background: rgba(231, 76, 60, 0.1);
    border-color: var(--accent-secondary);
    color: var(--accent-secondary);
}

.status-item.configured {
    background: rgba(46, 204, 113, 0.1);
    border-color: var(--status-success);
    color: var(--status-success);
}

.status-item.configured-disconnected {
    background: rgba(241, 196, 15, 0.1);
    border-color: var(--status-warning);
    color: var(--status-warning);
}

.status-item.configured-unknown {
    background: rgba(241, 196, 15, 0.1);
    border-color: var(--status-warning);
    color: var(--status-warning);
}

.status-item.connecting {
    background: rgba(241, 196, 15, 0.1);
    border-color: var(--status-warning);
    color: var(--status-warning);
}

.status-item.connected {
    background: rgba(46, 204, 113, 0.1);
    border-color: var(--status-success);
    color: var(--status-success);
}

.status-item.optional {
    background: rgba(149, 165, 166, 0.1);
    border-color: var(--text-secondary);
    color: var(--text-secondary);
}

.status-item.pending {
    background: rgba(241, 196, 15, 0.1);
    border-color: var(--status-warning);
    color: var(--status-warning);
}

.status-icon {
    font-size: 1.1em;
    font-weight: bold;
    min-width: 1.2em;
    text-align: center;
}

.status-help {
    font-size: 0.85em;
    margin-top: 0.25rem;
    opacity: 0.8;
    line-height: 1.3;
}

.status-help a {
    color: inherit;
    text-decoration: underline;
}

.status-text {
    font-size: 0.9rem;
    font-weight: 500;
}

.status-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.status-details p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    flex: 1;
}

/* Now Playing Section */
.now-playing-section {
    margin-bottom: 2rem;
}

.now-playing-section h2 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.now-playing-card {
    background: var(--bg-card);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    display: flex;
    gap: 2rem;
    align-items: center;
    box-shadow: 0 4px 20px var(--shadow-light);
    border: 1px solid var(--border-primary);
}

.album-art {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 1px solid var(--border-primary);
}

.album-art img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-music {
    text-align: center;
    color: var(--text-muted);
}

.no-music span {
    font-size: 2rem;
    display: block;
    margin-bottom: 0.5rem;
}

.track-info {
    flex: 1;
}

.track-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.track-info p {
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.track-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--accent-primary);
    width: 0%;
    transition: width 0.3s ease;
}

.time-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.zone-info {
    color: var(--text-secondary) !important;
    font-size: 0.9rem;
    font-style: italic;
}

.track-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-spotify {
    background: #1db954 !important;
    color: white !important;
    border: none;
}

.btn-spotify:hover {
    background: #1ed760 !important;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-bar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }

    .now-playing-card {
        flex-direction: column;
        text-align: center;
    }

    .quick-actions {
        flex-direction: column;
    }
}

/* Debug controls styles */
.debug-controls {
    background: var(--bg-secondary);
    border: 2px dashed #6b7280;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.debug-controls h3 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.debug-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.debug-status {
    display: flex;
    gap: 1rem;
    align-items: center;
    font-size: 0.9rem;
}

.debug-indicator {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.8rem;
}

.debug-indicator.debug-on {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid #22c55e;
}

.debug-indicator.debug-off {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
    border: 1px solid #9ca3af;
}

.debug-info {
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}