/* Configuration Styles */
.config-section {
    max-width: 800px;
    margin: 0 auto;
}

.config-section h2 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
    font-weight: 600;
}

.config-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    font-size: 1rem;
}

.config-group {
    background: var(--bg-card);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px var(--shadow-light);
    border: 1px solid var(--border-primary);
}

.config-group h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 600;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    min-width: 120px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group input[type="text"],
.form-group input[type="password"] {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid var(--border-primary);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.1);
}

.form-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent-primary);
}

.test-btn {
    background: var(--status-success);
    color: white;
    border: none;
    white-space: nowrap;
}

.test-btn:hover {
    background: #22c55e;
}

.help-text {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    line-height: 1.4;
}

.help-text a {
    color: var(--accent-primary);
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

.config-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

/* Configuration Status */
.config-status {
    background: var(--bg-card);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 20px var(--shadow-light);
    border-left: 4px solid var(--accent-primary);
    border: 1px solid var(--border-primary);
}

.status-summary h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.status-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

.status-item.configured {
    background: rgba(74, 222, 128, 0.1);
    color: var(--status-success);
}

.status-item.connected {
    background: rgba(46, 204, 113, 0.1);
    color: var(--status-success);
}

.status-item.missing {
    background: rgba(248, 113, 113, 0.1);
    color: var(--status-error);
}

.status-item.optional {
    background: rgba(128, 128, 128, 0.1);
    color: var(--text-muted);
}

.status-item.pending {
    background: rgba(251, 191, 36, 0.1);
    color: var(--status-warning);
}

.status-item.configured-unknown {
    background: rgba(241, 196, 15, 0.1);
    color: var(--status-warning);
}

.status-item.configured-disconnected {
    background: rgba(241, 196, 15, 0.1);
    color: var(--status-warning);
}

.status-item.connecting {
    background: rgba(241, 196, 15, 0.1);
    color: var(--status-warning);
}

.status-icon {
    font-weight: bold;
    font-size: 1rem;
}

.status-help {
    font-size: 0.8em;
    color: #666;
    margin-top: 4px;
    margin-left: 20px;
    font-style: italic;
}

.status-help a {
    color: #007acc;
    text-decoration: none;
}

.status-help a:hover {
    text-decoration: underline;
}

/* Logs Styles */
.logs-section {
    max-width: 1000px;
    margin: 0 auto;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.logs-header h2 {
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
}

.logs-controls {
    display: flex;
    gap: 0.5rem;
}

.logs-container {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid var(--border-primary);
    height: 500px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.logs-content {
    color: #e9ecef;
    font-size: 0.85rem;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 0.25rem;
    padding: 0.25rem 0;
    border-left: 3px solid transparent;
    padding-left: 0.75rem;
}

.log-entry.info {
    border-left-color: #3498db;
    color: #e9ecef;
}

.log-entry.success {
    border-left-color: #27ae60;
    color: #2ecc71;
}

.log-entry.warning {
    border-left-color: #f39c12;
    color: #f1c40f;
}

.log-entry.error {
    border-left-color: #e74c3c;
    color: #e74c3c;
}

.log-entry .timestamp {
    color: #95a5a6;
    font-size: 0.8rem;
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--bg-secondary);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-left: 4px solid var(--accent-primary);
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--error-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

/* Tooltips */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
    pointer-events: all;
}

.modal-content {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    color: var(--text-primary);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #666;
}

.modal-body {
    margin-bottom: 1.5rem;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .form-group label {
        min-width: auto;
    }
    
    .config-actions {
        flex-direction: column;
    }
    
    .logs-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .logs-controls {
        justify-content: center;
    }
    
    .modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    .modal-footer {
        flex-direction: column;
    }
}

/* Help Styles */
.help-quick-card {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s ease;
}

.help-quick-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-light);
    border-color: var(--accent-primary);
}

.help-quick-card h3 {
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.help-quick-card p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
    font-size: 0.9rem;
}
