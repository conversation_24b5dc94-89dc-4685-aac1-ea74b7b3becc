{"name": "roon-discord-publish", "version": "0.7.0", "description": "Roon Extension for Discord Rich Presence - GUI Edition", "main": "src/main/main.js", "homepage": "https://github.com/your-repo/roon-discord-publish", "scripts": {"start": "node start.js", "local": "node run-with-local-node.js", "setup-local-node": "node setup-local-node.js", "direct": "node roon-discord-publish.js", "dev": "node roon-discord-publish.js", "check-node": "node --version", "setup": "npm install && echo 'Please copy config.example.json to config.json and configure your settings'", "electron": "local-node/node-v16.20.2-win-x64/node.exe node_modules/electron/cli.js .", "electron-dev": "cross-env NODE_ENV=development local-node/node-v16.20.2-win-x64/node.exe node_modules/electron/cli.js .", "electron-debug": "cross-env NODE_ENV=development local-node/node-v16.20.2-win-x64/node.exe node_modules/electron/cli.js . --inspect=5858", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "test": "playwright test", "test-config": "node test-config-validation.js", "test:gui": "playwright test --headed", "test:screenshot": "node test/cli/screenshot.js", "test:status": "node test/cli/status-check.js", "test:integration": "node test/cli/integration-test.js", "test:workflow": "playwright test test/e2e/workflow.test.js", "test:integration-e2e": "playwright test test/e2e/integration.test.js", "test:all": "npm run test:integration && npm run test:workflow", "debug:diagnostics": "node test/cli/debug-diagnostics.js", "debug:verbose": "NODE_ENV=development npm run electron-dev", "test:interactive": "node test/cli/interactive.js", "test:report": "playwright show-report test-results/html-report", "pack": "electron-builder --dir", "dist": "npm run build", "clean": "<PERSON><PERSON><PERSON> dist", "rebuild": "electron-rebuild", "postinstall": "electron-builder install-app-deps", "create-exe": "pkg legacy/launcher.js --targets node16-win-x64 --output \"Roon Discord Rich Presence.exe\" && node scripts/add-icon.js", "create-distribution": "npm run create-exe && node scripts/create-distribution.js", "console": "node legacy/roon-discord-publish.js", "package-clean": "rimraf working-build distribution* release-build final-build packager-dist portable* \"Roon Discord Rich Presence.exe\""}, "engines": {"node": ">=16.13.2 <17.0.0", "npm": ">=8.0.0"}, "author": "615283 (<PERSON>), synapses, jar<PERSON><PERSON><PERSON>", "license": "Apache-2.0", "dependencies": {"discord-rpc": "^4.0.1", "form-data": "^4.0.4", "imgur-anonymous-uploader": "^1.1.6", "imgur-node-api": "^0.1.0", "node-fetch": "^3.3.2", "node-roon-api": "github:roonlabs/node-roon-api", "node-roon-api-image": "github:RoonLabs/node-roon-api-image", "node-roon-api-transport": "github:roonlabs/node-roon-api-transport", "spotify-web-api-node": "^5.0.2"}, "devDependencies": {"@playwright/test": "^1.54.2", "cross-env": "^7.0.3", "electron": "^28.3.3", "electron-builder": "^24.13.3", "electron-rebuild": "^3.2.9", "electron-reload": "^2.0.0-alpha.1", "playwright": "^1.54.2", "resedit": "^2.0.3", "rimraf": "^5.0.10", "screenshot-desktop": "^1.15.2"}, "build": {"appId": "com.echofox.roon-discord-publish", "productName": "<PERSON><PERSON> Discord Rich Presence", "directories": {"output": "dist"}, "files": ["src/**/*", "config.example.json", "node_modules/**/*", "!node_modules/.cache/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}